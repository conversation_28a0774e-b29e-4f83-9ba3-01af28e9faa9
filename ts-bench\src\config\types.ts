export type AgentType = 'claude' | 'goose' | 'aider' | 'codex' | 'gemini' | 'opencode' | 'qwen' | 'cursor';
export type ProviderType = 'openai' | 'anthropic' | 'google' | 'openrouter' | 'dashscope' | 'xai' | 'deepseek';

export interface AgentResult {
    exercise: string;
    success: boolean;
    error?: string;
    duration: number;
    output?: string;
    workspacePath?: string;
    score?: number;
    details?: any;
}

export interface TestResult {
    exercise: string;
    agentSuccess: boolean;
    testSuccess: boolean;
    overallSuccess: boolean;
    agentError?: string;
    testError?: string;
    agentDuration: number;
    testDuration: number;
    totalDuration: number;
}

export interface TestOnlyResult {
    exercise: string;
    testSuccess: boolean;
    testError?: string;
    testDuration: number;
    output?: string;
}

export interface BenchmarkConfig {
    testCommand: string;
    agent: AgentType;
    model: string;
    provider: ProviderType;
    verbose: boolean;
    useDocker?: boolean;
    version?: string;
    showProgress?: boolean;
    timeout?: number; // seconds
    outputDir?: string;
}

export interface CLIArgs {
    // Execution mode selection
    /** Execution mode: 'exercise' for file-based exercises, 'test-suite' for prompt-based test cases */
    mode?: 'exercise' | 'test-suite';

    // Model and agent configuration
    model: string;
    agent: AgentType;
    provider: ProviderType;
    verbose: boolean;

    // Exercise mode options
    specificExercise: string | null;
    exerciseCount: number | null;
    exerciseList?: string[];
    listExercises: boolean;
    exercismPath?: string;

    // Test suite mode options
    /** Specific test suite to run */
    testSuite?: string;
    /** List of test suites to run */
    testSuiteList?: string[];
    /** List all available test suites */
    listTestSuites?: boolean;
    /** Custom path to test suite directory */
    testSuitePath?: string;
    /** Enable skatebench-compatible output format */
    skatebenchOutput?: boolean;
    /** Maximum number of concurrent test executions */
    maxConcurrency?: number;
    /** Path to save leaderboard file */
    leaderboardPath?: string;

    // Output and execution settings
    outputFormat?: 'console' | 'json';
    outputDir?: string;
    exportWeb?: boolean;
    allAgents?: boolean;
    batch?: number;
    totalBatches?: number;
    useDocker?: boolean;
    saveResult?: boolean;
    resultName?: string;
    resultDir?: string;
    generateLeaderboard?: boolean;
    updateLeaderboard?: boolean;
    version?: string;
    showProgress?: boolean;
    testOnly?: boolean;
    printInstructions?: boolean;
    customInstruction?: string;
    timeout?: number; // seconds
}


export type OutputFormat = 'console' | 'json';
