import { TestSuiteReader } from '../test-suites/reader';
import { TestSuiteRunner } from '../runners/test-suite';
import { TestSuiteBenchmarkReporter } from './test-suite-reporter';
import { CLIArgs, BenchmarkConfig } from '../config/types';
import { TestSuiteResult, TestSuiteBenchmarkResult } from '../config/test-suite-types';
import { Logger } from '../utils/logger';
import * as path from 'path';
import * as fs from 'fs/promises';

/**
 * TestSuiteBenchmarkRunner handles test suite-based benchmarking
 * Replaces BenchmarkRunner for test suite mode
 */
export class TestSuiteBenchmarkRunner {
    private testSuiteReader: TestSuiteReader;
    private testSuiteRunner: TestSuiteRunner;
    private reporter: TestSuiteBenchmarkReporter;
    private logger: Logger;

    constructor(
        testSuiteReader: TestSuiteReader,
        testSuiteRunner: Test<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        reporter: TestSuiteBenchmarkReporter,
        logger: Logger
    ) {
        this.testSuiteReader = testSuiteReader;
        this.testSuiteRunner = testSuiteRunner;
        this.reporter = reporter;
        this.logger = logger;
    }

    /**
     * Main entry point for test suite benchmarking
     */
    async run(args: CLIArgs): Promise<TestSuiteBenchmarkResult> {
        this.logger.info('Starting test suite benchmark execution');

        // Handle list test suites option
        if (args.listTestSuites) {
            await this.listTestSuites(args);
            process.exit(0);
        }

        // Determine which test cases to run
        const testCases = await this.selectTestCases(args);
        if (testCases.length === 0) {
            throw new Error('No test cases selected for execution');
        }

        this.logger.info(`Selected ${testCases.length} test cases for execution`);

        // Create benchmark configuration
        const config: BenchmarkConfig = {
            testCommand: '', // Not used for test suites
            agent: args.agent,
            model: args.model,
            provider: args.provider,
            verbose: args.verbose,
            useDocker: args.useDocker,
            timeout: args.timeout,
            outputDir: args.outputDir
        };

        // Execute test cases
        const results = await this.executeTestCases(config, testCases, args);

        // Generate benchmark result
        const benchmarkResult = this.createBenchmarkResult(results, testCases);

        // Output results
        await this.outputResults(benchmarkResult, config, args);

        // Generate leaderboard if requested
        if (args.generateLeaderboard) {
            await this.generateLeaderboard(benchmarkResult, args);
        }

        return benchmarkResult;
    }

    /**
     * List all available test suites
     */
    private async listTestSuites(args: CLIArgs): Promise<void> {
        const suiteIds = await this.testSuiteReader.listSuites();

        console.log('\nAvailable Test Suites:');
        console.log('======================');

        for (const suiteId of suiteIds) {
            try {
                const suiteInfo = await this.testSuiteReader.getSuiteInfo(suiteId);
                const suitePath = this.testSuiteReader.getSuitePath(suiteId);

                console.log(`\n${suiteId}:`);
                console.log(`  Description: ${suiteInfo.description || 'No description'}`);
                console.log(`  Test Cases: ${suiteInfo.testCount || 0}`);
                console.log(`  Path: ${suitePath}`);
            } catch (error) {
                console.log(`\n${suiteId}:`);
                console.log(`  Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }

        console.log(`\nTotal: ${suiteIds.length} test suites found`);
    }

    /**
     * Select test cases based on CLI arguments
     */
    private async selectTestCases(args: CLIArgs): Promise<Array<{ suiteId: string; testCaseId: string }>> {
        const testCases: Array<{ suiteId: string; testCaseId: string }> = [];

        if (args.testSuite) {
            // Run specific test suite
            const testSuite = await this.testSuiteReader.loadSuite(args.testSuite);
            if (!testSuite) {
                throw new Error(`Test suite '${args.testSuite}' not found`);
            }

            for (const testCase of testSuite.tests) {
                testCases.push({ suiteId: args.testSuite, testCaseId: testCase.id });
            }
        } else if (args.testSuiteList && args.testSuiteList.length > 0) {
            // Run multiple test suites
            for (const suiteId of args.testSuiteList) {
                try {
                    const testSuite = await this.testSuiteReader.loadSuite(suiteId);
                    for (const testCase of testSuite.tests) {
                        testCases.push({ suiteId, testCaseId: testCase.id });
                    }
                } catch (error) {
                    this.logger.warn(`Test suite '${suiteId}' not found, skipping: ${error instanceof Error ? error.message : 'Unknown error'}`);
                    continue;
                }
            }
        } else {
            // Run all test suites
            const availableTestSuites = await this.testSuiteReader.listSuites();

            for (const suiteId of availableTestSuites) {
                try {
                    const testSuite = await this.testSuiteReader.loadSuite(suiteId);
                    for (const testCase of testSuite.tests) {
                        testCases.push({ suiteId, testCaseId: testCase.id });
                    }
                } catch (error) {
                    this.logger.warn(`Failed to load test suite '${suiteId}', skipping: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        }

        return testCases;
    }

    /**
     * Execute all selected test cases
     */
    private async executeTestCases(
        config: BenchmarkConfig,
        testCases: Array<{ suiteId: string; testCaseId: string }>,
        args: CLIArgs
    ): Promise<TestSuiteResult[]> {
        const results: TestSuiteResult[] = [];
        const maxConcurrency = args.maxConcurrency || 1;
        
        this.logger.info(`Executing ${testCases.length} test cases with concurrency ${maxConcurrency}`);

        // Execute test cases in batches
        for (let i = 0; i < testCases.length; i += maxConcurrency) {
            const batch = testCases.slice(i, i + maxConcurrency);
            const batchPromises = batch.map(async (testCase) => {
                try {
                    const result = await this.testSuiteRunner.run(config, testCase.suiteId, testCase.testCaseId);
                    this.logger.info(`Completed ${testCase.suiteId}:${testCase.testCaseId} - ${result.correct ? 'CORRECT' : 'INCORRECT'}`);
                    return result;
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    this.logger.error(`Failed ${testCase.suiteId}:${testCase.testCaseId} - ${errorMessage}`);
                    
                    return {
                        suiteId: testCase.suiteId,
                        testCaseId: testCase.testCaseId,
                        prompt: '',
                        model: config.model,
                        agent: config.agent,
                        provider: config.provider,
                        success: false,
                        correct: false,
                        error: errorMessage,
                        duration: 0,
                        timestamp: new Date()
                    } as TestSuiteResult;
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);

            // Progress reporting
            const completed = i + batch.length;
            const percentage = Math.round((completed / testCases.length) * 100);
            this.logger.info(`Progress: ${completed}/${testCases.length} (${percentage}%)`);
        }

        return results;
    }

    /**
     * Create benchmark result from test results
     */
    private createBenchmarkResult(
        results: TestSuiteResult[],
        testCases: Array<{ suiteId: string; testCaseId: string }>
    ): TestSuiteBenchmarkResult {
        const suiteIds = [...new Set(testCases.map(tc => tc.suiteId))];
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
        const totalCost = results.reduce((sum, r) => sum + (r.cost || 0), 0);

        // Generate model rankings
        const rankings = this.reporter.generateModelPerformance(results);

        return {
            rankings,
            metadata: {
                totalTestCases: results.length,
                totalSuites: suiteIds.length,
                executionDate: new Date(),
                totalDuration,
                totalCost,
                suiteIds
            },
            rawResults: results
        };
    }

    /**
     * Output results in various formats
     */
    private async outputResults(
        benchmarkResult: TestSuiteBenchmarkResult,
        config: BenchmarkConfig,
        args: CLIArgs
    ): Promise<void> {
        // Console output
        if (args.skatebenchOutput) {
            this.reporter.printSkatebenchResults(benchmarkResult.rawResults, config);
        } else {
            this.reporter.printResults(benchmarkResult.rawResults);
        }

        // File output
        if (args.outputDir) {
            const outputPath = path.join(args.outputDir, `test-suite-${config.agent}-${config.model}-${Date.now()}.json`);
            
            if (args.skatebenchOutput) {
                await this.reporter.exportToSkatebenchJSON(benchmarkResult.rawResults, config, outputPath);
            } else {
                await this.reporter.saveTestSuiteResult(benchmarkResult.rawResults, config, outputPath);
            }
            
            this.logger.info(`Results saved to: ${outputPath}`);
        }
    }

    /**
     * Generate leaderboard from results
     */
    private async generateLeaderboard(
        benchmarkResult: TestSuiteBenchmarkResult,
        args: CLIArgs
    ): Promise<void> {
        const leaderboardPath = args.leaderboardPath || './leaderboard-test-suite.json';
        
        // Create leaderboard data
        const leaderboardData = {
            lastUpdated: new Date().toISOString(),
            totalTestCases: benchmarkResult.metadata.totalTestCases,
            totalSuites: benchmarkResult.metadata.totalSuites,
            rankings: benchmarkResult.rankings.map(ranking => ({
                model: ranking.model,
                agent: ranking.agent,
                provider: ranking.provider,
                successRate: ranking.successRate,
                errorRate: ranking.errorRate,
                averageDuration: ranking.averageDuration,
                totalCost: ranking.totalCost,
                totalTests: ranking.totalTests
            }))
        };

        await fs.writeFile(leaderboardPath, JSON.stringify(leaderboardData, null, 2));
        this.logger.info(`Leaderboard saved to: ${leaderboardPath}`);
    }
}
